import os
import torch
from torch.utils.data import Dataset
from tqdm import tqdm
import jieba
import zhconv
import re
from collections import Counter


class SimpleVocab:
    """简单的词典类"""
    def __init__(self, vocab_dict):
        self.vocab_dict = vocab_dict
        self.idx_to_token = {v: k for k, v in vocab_dict.items()}
        self.unk_index = vocab_dict["<unk>"]

    def __getitem__(self, token):
        return self.vocab_dict.get(token, self.unk_index)

    def __call__(self, tokens):
        return [self[token] for token in tokens]

    def __len__(self):
        return len(self.vocab_dict)

    def lookup_token(self, idx):
        return self.idx_to_token.get(idx, "<unk>")

    def lookup_tokens(self, indices):
        return [self.lookup_token(idx) for idx in indices]


class TranslationDataset(Dataset):
    def __init__(self, filepath, use_cache=True):
        self.row_count = self.get_row_count(filepath)
        self.use_cache = use_cache

        # 加载词典和token
        self.de_vocab = self.get_de_vocab(filepath)
        self.zh_vocab = self.get_zh_vocab(filepath)
        self.de_tokens = self.load_tokens(filepath, self.de_tokenizer_func, self.de_vocab, "构建德文tokens", 'de')
        self.zh_tokens = self.load_tokens(filepath, self.zh_tokenizer, self.zh_vocab, "构建中文tokens", 'zh')

    def __getitem__(self, index):
        return self.de_tokens[index], self.zh_tokens[index]

    def __len__(self):
        return self.row_count

    def get_row_count(self, filepath):
        count = 0
        for _ in open(filepath, encoding='utf-8'):
            count += 1
        return count

    def de_tokenizer_func(self, line):
        # 简单的德语分词：按空格和标点符号分割
        line = line.lower().strip()
        # 使用正则表达式分词
        tokens = re.findall(r'\b\w+\b', line)
        return tokens

    def zh_tokenizer(self, line):
        return list(jieba.cut(line))

    def get_de_vocab(self, filepath):
        def yield_de_tokens():
            with open(filepath, encoding='utf-8') as f:
                print("---开始构建德文词典---")
                for line in tqdm(f, desc="构建德文词典", total=self.row_count):
                    sentence = line.split('\t')
                    if len(sentence) >= 1:
                        german = sentence[0]
                        yield self.de_tokenizer_func(german)

        dir_path = os.path.dirname(filepath)
        de_vocab_file = os.path.join(dir_path, "vocab_de.pt")
        de_vocab = None

        if self.use_cache and os.path.exists(de_vocab_file):
            try:
                de_vocab = torch.load(de_vocab_file, map_location="cpu")
                print(f"成功加载德语词典缓存: {de_vocab_file}")
            except Exception as e:
                print(f"加载德语词典缓存失败: {e}")
                print("重新构建德语词典...")
                # 删除损坏的缓存文件
                os.remove(de_vocab_file)
                de_vocab = None

        if de_vocab is None:
            # 手动构建词典
            word_counts = Counter()
            for tokens in yield_de_tokens():
                word_counts.update(tokens)

            # 创建词典
            vocab_dict = {"<s>": 0, "</s>": 1, "<pad>": 2, "<unk>": 3}
            idx = 4
            for word, count in word_counts.items():
                if count >= 2:  # min_freq=2
                    vocab_dict[word] = idx
                    idx += 1

            de_vocab = SimpleVocab(vocab_dict)
            if self.use_cache:
                try:
                    torch.save(de_vocab, de_vocab_file)
                    print(f"德语词典缓存已保存: {de_vocab_file}")
                except Exception as e:
                    print(f"保存德语词典缓存失败: {e}")
        return de_vocab

    def get_zh_vocab(self, filepath):
        def yield_zh_tokens():
            with open(filepath, encoding='utf-8') as f:
                print("---开始构建中文词典---")
                for line in tqdm(f, desc="构建中文词典", total=self.row_count):
                    sentence = line.split('\t')
                    if len(sentence) >= 2:
                        chinese = zhconv.convert(sentence[1], 'zh-cn')
                        yield self.zh_tokenizer(chinese)

        dir_path = os.path.dirname(filepath)
        zh_vocab_file = os.path.join(dir_path, "vocab_zh.pt")
        zh_vocab = None

        if self.use_cache and os.path.exists(zh_vocab_file):
            try:
                zh_vocab = torch.load(zh_vocab_file, map_location="cpu")
                print(f"成功加载中文词典缓存: {zh_vocab_file}")
            except Exception as e:
                print(f"加载中文词典缓存失败: {e}")
                print("重新构建中文词典...")
                # 删除损坏的缓存文件
                os.remove(zh_vocab_file)
                zh_vocab = None

        if zh_vocab is None:
            # 手动构建中文词典
            word_counts = Counter()
            for tokens in yield_zh_tokens():
                word_counts.update(tokens)

            # 创建词典
            vocab_dict = {"<s>": 0, "</s>": 1, "<pad>": 2, "<unk>": 3}
            idx = 4
            for word, count in word_counts.items():
                if count >= 1:  # min_freq=1
                    vocab_dict[word] = idx
                    idx += 1

            zh_vocab = SimpleVocab(vocab_dict)
            if self.use_cache:
                try:
                    torch.save(zh_vocab, zh_vocab_file)
                    print(f"中文词典缓存已保存: {zh_vocab_file}")
                except Exception as e:
                    print(f"保存中文词典缓存失败: {e}")
        return zh_vocab

    def load_tokens(self, filepath, tokenizer, vocab, desc, lang):
        dir_path = os.path.dirname(filepath)
        cache_file = os.path.join(dir_path, f"tokens_list_{lang}.pt")
        if self.use_cache and os.path.exists(cache_file):
            try:
                print(f"正在加载缓存文件[{cache_file}]，请稍候...")
                return torch.load(cache_file, map_location="cpu")
            except Exception as e:
                print(f"加载tokens缓存失败: {e}")
                print("重新构建tokens...")
                # 删除损坏的缓存文件
                os.remove(cache_file)

        tokens_list = []
        with open(filepath, encoding='utf-8') as f:
            for line in tqdm(f, desc=desc, total=self.row_count):
                sentence = line.strip().split('\t')
                if (lang == 'de' and len(sentence) >= 1) or (lang != 'de' and len(sentence) >= 2):
                    if lang == 'de':
                        text = sentence[0].casefold()
                    else:
                        text = zhconv.convert(sentence[1], 'zh-cn')
                    tokens = tokenizer(text)
                    token_indices = [vocab[token] for token in tokens]
                    token_tensor = torch.tensor([vocab["<s>"]] + token_indices + [vocab["</s>"]])
                    tokens_list.append(token_tensor)

        if self.use_cache:
            try:
                torch.save(tokens_list, cache_file)
                print(f"{lang}语tokens缓存已保存: {cache_file}")
            except Exception as e:
                print(f"保存{lang}语tokens缓存失败: {e}")
        return tokens_list


if __name__ == '__main__':
    dataset = TranslationDataset(r"D:\my-deeplearning\data\de-zhz.txt\dezh1.txt")
    print("句子数量为:", dataset.row_count)
    print(dataset.de_tokenizer_func("Ich bin ein deutscher Tokenizer."))  # 德语分词示例
    print("德文词典大小:", len(dataset.de_vocab))
    # 输出德文词典前10个索引
    print(dict((i, dataset.de_vocab.lookup_token(i)) for i in range(10)))
    print("中文词典大小:", len(dataset.zh_vocab))
    # 输出中文词典前10个索引
    print(dict((i, dataset.zh_vocab.lookup_token(i)) for i in range(10)))
    # 输出德文前10个句子对应的字典索引编号
    print(dict((i, dataset.de_tokens[i]) for i in range(10)))
    # 输出中文前10个句子对应的字典索引编号
    print(dict((i, dataset.zh_tokens[i]) for i in range(10)))
    print(dataset.de_vocab(['hallo', 'welt']))  # 该词在词典中的索引
    print(dataset.zh_vocab(['你', '好', '世', '界']))  # 该词在词典中的索引
