#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
德中翻译模型训练启动脚本
"""

import os
import sys
from pathlib import Path

def main():
    print("=" * 60)
    print("德中翻译 Transformer 模型训练")
    print("=" * 60)
    
    # 检查数据文件是否存在
    data_file = Path("data/de-zhz.txt/dezh1.txt")
    if not data_file.exists():
        print(f"错误: 数据文件不存在: {data_file}")
        print("请确保德中翻译数据集文件存在。")
        return
    
    print(f"✓ 数据文件存在: {data_file}")
    print(f"✓ 数据文件大小: {data_file.stat().st_size / (1024*1024):.1f} MB")
    
    # 检查必要的目录
    train_dir = Path("train_process/transformer-dezh")
    if not train_dir.exists():
        print(f"创建训练目录: {train_dir}")
        train_dir.mkdir(parents=True, exist_ok=True)
    
    model_dir = train_dir / "transformer_checkpoints"
    if not model_dir.exists():
        print(f"创建模型目录: {model_dir}")
        model_dir.mkdir(parents=True, exist_ok=True)
    
    print("\n训练参数:")
    print("- 批大小: 256")
    print("- 训练轮数: 50")
    print("- 最大序列长度: 24")
    print("- 学习率: 0.0001")
    print("- 模型维度: 256")
    print("- 每5000步保存一次模型")
    print("- 每个epoch保存最佳模型")
    
    print("\n开始训练...")
    print("=" * 60)
    
    # 导入并开始训练
    try:
        from train_dezh_transformer import train
        train()
    except KeyboardInterrupt:
        print("\n训练被用户中断")
    except Exception as e:
        print(f"\n训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n训练完成！")
    print("模型保存在: train_process/transformer-dezh/transformer_checkpoints/")

if __name__ == "__main__":
    main()
