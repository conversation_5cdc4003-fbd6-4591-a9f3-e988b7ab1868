#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
德中翻译测试脚本
"""

import os
import sys
from pathlib import Path

def test_translation():
    print("=" * 60)
    print("德中翻译模型测试")
    print("=" * 60)
    
    # 检查模型文件是否存在
    model_dir = Path("train_process/transformer-dezh/transformer_checkpoints")
    best_model = model_dir / "best.pt"
    
    if not best_model.exists():
        print("错误: 找不到训练好的模型文件")
        print(f"请确保模型文件存在: {best_model}")
        print("请先运行训练脚本: python start_training.py")
        return
    
    print(f"✓ 找到模型文件: {best_model}")
    
    try:
        from interface_dezh_transformer import translate
        
        # 测试句子
        test_sentences = [
            "Hallo Welt",
            "Ich liebe dich",
            "Deutschland ist ein schönes Land",
            "Maschinelles Lernen ist interessant",
            "Der Zug kommt um acht Uhr an",
            "Wie geht es dir heute?",
            "Berlin ist die Hauptstadt von Deutschland",
            "Ich möchte Chinesisch lernen"
        ]
        
        print("\n开始翻译测试:")
        print("-" * 60)
        
        for i, sentence in enumerate(test_sentences, 1):
            print(f"{i}. 德语: {sentence}")
            try:
                translation = translate(sentence)
                print(f"   中文: {translation}")
            except Exception as e:
                print(f"   翻译失败: {e}")
            print()
        
        print("=" * 60)
        print("交互式翻译模式 (输入 'quit' 退出):")
        print("=" * 60)
        
        while True:
            try:
                user_input = input("\n请输入德语句子: ").strip()
                if user_input.lower() in ['quit', 'exit', 'q']:
                    break
                if user_input:
                    translation = translate(user_input)
                    print(f"中文翻译: {translation}")
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"翻译错误: {e}")
        
        print("\n测试完成！")
        
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保所有依赖项已正确安装")
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_translation()
