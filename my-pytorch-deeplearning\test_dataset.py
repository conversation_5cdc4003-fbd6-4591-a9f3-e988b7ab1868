#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from dataset.dezh import TranslationDataset

def test_dataset():
    print("开始测试德中翻译数据集...")
    
    # 创建数据集实例
    dataset = TranslationDataset("data/de-zhz.txt/dezh1.txt")
    
    print(f"数据集大小: {len(dataset)}")
    print(f"德语词典大小: {len(dataset.de_vocab)}")
    print(f"中文词典大小: {len(dataset.zh_vocab)}")
    
    # 测试德语分词
    test_de = "Hallo Welt, wie geht es dir?"
    de_tokens = dataset.de_tokenizer_func(test_de)
    print(f"德语分词测试: '{test_de}' -> {de_tokens}")
    
    # 测试中文分词
    test_zh = "你好世界，你好吗？"
    zh_tokens = dataset.zh_tokenizer(test_zh)
    print(f"中文分词测试: '{test_zh}' -> {zh_tokens}")
    
    # 测试词典查找
    print(f"德语词典前10个词: {[dataset.de_vocab.lookup_token(i) for i in range(10)]}")
    print(f"中文词典前10个词: {[dataset.zh_vocab.lookup_token(i) for i in range(10)]}")
    
    # 测试数据样本
    if len(dataset) > 0:
        de_sample, zh_sample = dataset[0]
        print(f"第一个样本:")
        print(f"  德语tokens: {de_sample}")
        print(f"  中文tokens: {zh_sample}")
        print(f"  德语文本: {' '.join(dataset.de_vocab.lookup_tokens(de_sample.tolist()))}")
        print(f"  中文文本: {''.join(dataset.zh_vocab.lookup_tokens(zh_sample.tolist()))}")
    
    print("数据集测试完成！")

if __name__ == "__main__":
    test_dataset()
