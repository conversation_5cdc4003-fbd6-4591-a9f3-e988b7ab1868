# 德中翻译 Transformer 模型

本项目已从中英翻译修改为德中翻译，使用 Transformer 架构进行神经机器翻译。

## 项目结构

```
my-pytorch-deeplearning/
├── data/
│   └── de-zhz.txt/
│       └── dezh1.txt          # 德中翻译数据集
├── dataset/
│   └── dezh.py                # 德中数据集处理类
├── model/
│   └── transformer.py        # Transformer 模型定义
├── train_dezh_transformer.py # 训练脚本
├── interface_dezh_transformer.py # 推理接口
├── test_dataset.py           # 数据集测试脚本
└── train_process/
    └── transformer-dezh/      # 训练输出目录
        ├── transformer_checkpoints/ # 模型检查点
        └── logs/              # TensorBoard 日志
```

## 主要修改

### 1. 数据集修改 (dataset/dezh.py)
- 将英文处理改为德文处理
- 使用简单的正则表达式进行德语分词
- 移除了对 torchtext 的依赖，使用自定义词典类
- 支持德语-中文语言对处理

### 2. 训练脚本修改 (train_dezh_transformer.py)
- 更新数据路径为德中数据集
- 修改模型初始化使用德语和中文词典
- **新增功能**: 每个 epoch 结束后保存最佳模型
- 改进的损失跟踪和模型保存逻辑

### 3. 推理接口修改 (interface_dezh_transformer.py)
- 修改为德中翻译接口
- 更新输入处理为德语文本
- 修改示例测试用例为德语句子

### 4. 模型文件修改 (model/transformer.py)
- 更新示例代码使用德中数据集
- 修改测试用例为德语输入

## 新增功能

### 每次训练保存最佳模型
训练脚本现在会：
1. 在每个 epoch 结束后计算平均损失
2. 如果当前 epoch 的平均损失是最佳的，保存为 `best_epoch_{epoch_number}.pt`
3. 同时保持原有的定期保存机制（每 5000 步保存一次）

## 使用方法

### 1. 训练模型
```bash
cd my-pytorch-deeplearning
python train_dezh_transformer.py
```

### 2. 测试数据集
```bash
python test_dataset.py
```

### 3. 使用训练好的模型进行翻译
```bash
python interface_dezh_transformer.py
```

或者在代码中使用：
```python
from interface_dezh_transformer import translate

# 德语到中文翻译
result = translate("Ich mag maschinelles Lernen.")
print(result)  # 输出中文翻译
```

## 数据格式

数据集文件 `data/de-zhz.txt/dezh1.txt` 的格式为：
```
德语文本\t中文文本
Deutschland\t德国
Hallo Welt\t你好世界
```

## 训练参数

- 批大小: 256
- 训练轮数: 50
- 最大序列长度: 24
- 学习率: 0.0001
- 模型维度: 256

## 模型保存

训练过程中会保存以下模型：
- `model_{step}.pt`: 每 5000 步保存的检查点
- `best.pt`: 当前最佳模型（基于单步损失）
- `best_epoch_{epoch}.pt`: 每个 epoch 的最佳模型（基于平均损失）

## 依赖项

- PyTorch
- jieba (中文分词)
- zhconv (中文繁简转换)
- tqdm (进度条)
- tensorboard (训练日志)

## 注意事项

1. 确保数据集路径正确
2. 训练前会自动创建必要的目录
3. 支持 GPU 加速（如果可用）
4. 使用缓存机制加速数据加载
